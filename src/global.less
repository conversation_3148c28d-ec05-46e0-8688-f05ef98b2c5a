@font-face {
  font-weight: 300;
  font-family: "AlibabaSans";
  font-style: normal;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*1GSgSYDD_aIAAAAAQsAAAAgAegCCAQ/AlibabaSans-Light.woff2")
    format("woff2");
  font-display: swap;
}
@font-face {
  font-weight: 400;
  font-family: "AlibabaSans";
  font-style: normal;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*2zEUQqnPNesAAAAAQtAAAAgAegCCAQ/AlibabaSans-Regular.woff2")
    format("woff2");
  font-display: swap;
}
@font-face {
  font-weight: 500;
  font-family: "AlibabaSans";
  font-style: normal;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Medium.woff2")
    format("woff2");
  font-display: swap;
}
@font-face {
  font-weight: 600;
  font-family: "AlibabaSans";
  font-style: normal;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Bold.woff2")
    format("woff2");
  font-display: swap;
}
@font-face {
  font-weight: 700;
  font-family: "AlibabaSans";
  font-style: normal;
  src: url("//mdn.alipayobjects.com/huamei_iwk9zp/afts/file/A*E_cxRbMlZqUAAAAAQuAAAAgAegCCAQ/AlibabaSans-Heavy.woff2")
    format("woff2");
  font-display: swap;
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: AlibabaSans, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* Gradient background for the margin areas */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100%; /* Fill the container */
  background-color: transparent; /* Let container handle background */
  border-radius: 0; /* Container handles border radius */
  box-shadow: none; /* Container handles shadow */
}

/* Ensure ProLayout header works well in constrained layout */
.ant-pro-global-header {
  border-radius: 16px 16px 0 0; /* Match container border radius */
}

/* Adjust content area for better mobile experience */
.ant-pro-page-container-children-content {
  margin: 16px;
}

@media (max-width: 768px) {
  .ant-pro-page-container-children-content {
    margin: 8px;
  }
}
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .ant-pro-global-header-collapsed-button {
    display: none !important;
  }
}
