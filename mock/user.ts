import type { Request, Response } from 'express';

// 代码中会兼容本地 service mock 以及部署站点的静态数据
export default {
    // GET POST 可省略
    'GET /api/users': [
        {
            key: '1',
            name: '<PERSON>',
            age: 32,
            address: 'New York No. 1 Lake Park',
        },
        {
            key: '2',
            name: '<PERSON>',
            age: 42,
            address: 'London No. 1 Lake Park',
        },
        {
            key: '3',
            name: '<PERSON>',
            age: 32,
            address: 'Sidney No. 1 Lake Park',
        },
    ],

    'GET /api/500': (_req: Request, res: Response) => {
        res.status(500).send({
            timestamp: 1513932555104,
            status: 500,
            error: 'error',
            message: 'error',
            path: '/base/category/list',
        });
    },
    'GET /api/404': (_req: Request, res: Response) => {
        res.status(404).send({
            timestamp: 1513932643431,
            status: 404,
            error: 'Not Found',
            message: 'No message available',
            path: '/base/category/list/2121212',
        });
    },
    'GET /api/403': (_req: Request, res: Response) => {
        res.status(403).send({
            timestamp: 1513932555104,
            status: 403,
            error: 'Forbidden',
            message: 'Forbidden',
            path: '/base/category/list',
        });
    },
    'GET /api/401': (_req: Request, res: Response) => {
        res.status(401).send({
            timestamp: 1513932555104,
            status: 401,
            error: 'Unauthorized',
            message: 'Unauthorized',
            path: '/base/category/list',
        });
    },


};
